<!DOCTYPE html>
<html lang="en">
    <head>
        <title>Flowise - Build AI Agents, Visually</title>
        <link rel="icon" href="favicon.ico" />
        <!-- Meta Tags-->
        <meta charset="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta name="theme-color" content="#2296f3" />
        <meta name="title" content="Flowise - Build AI Agents, Visually" />
        <meta
            name="description"
            content="Open source generative AI development platform for building AI agents, LLM orchestration, and more"
        />
        <link rel="manifest" href="manifest.json" />
        <link rel="apple-touch-icon" href="logo192.png" />
        <meta name="keywords" content="react, material-ui, workflow automation, llm, artificial-intelligence" />
        <meta name="author" content="FlowiseAI" />
        <!-- Open Graph / Facebook -->
        <meta property="og:locale" content="en_US" />
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://flowiseai.com/" />
        <meta property="og:site_name" content="flowiseai.com" />
        <meta property="og:title" content="Flowise - Build AI Agents, Visually" />
        <meta
            property="og:description"
            content="Open source generative AI development platform for building AI agents, LLM orchestration, and more"
        />
        <!-- Twitter -->
        <meta property="twitter:card" content="summary_large_image" />
        <meta property="twitter:url" content="https://twitter.com/FlowiseAI" />
        <meta property="twitter:title" content="Flowise - Build AI Agents, Visually" />
        <meta
            property="twitter:description"
            content="Open source generative AI development platform for building AI agents, LLM orchestration, and more"
        />
        <meta name="twitter:creator" content="@FlowiseAI" />

        <link rel="preconnect" href="https://fonts.gstatic.com" />
        <link
            href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Poppins:wght@400;500;600;700&family=Roboto:wght@400;500;700&display=swap"
            rel="stylesheet"
        />
        <script>
            ;(function (w, r) {
                w._rwq = r
                w[r] =
                    w[r] ||
                    function () {
                        ;(w[r].q = w[r].q || []).push(arguments)
                    }
            })(window, 'rewardful')
        </script>
        <script async src="https://r.wdfl.co/rw.js" data-rewardful="9a3a26"></script>
    </head>

    <body>
        <noscript>You need to enable JavaScript to run this app.</noscript>
        <div id="root"></div>
        <div id="portal"></div>
        <script type="module" src="src/index.jsx"></script>
        <script>
            if (global === undefined) {
                var global = window
            }
        </script>
    </body>
</html>
