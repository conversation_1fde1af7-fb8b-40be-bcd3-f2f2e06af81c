{"description": "An agent designed to use tools and LLM with function calling capability to provide responses", "usecases": ["Agent"], "framework": ["Langchain"], "nodes": [{"width": 300, "height": 149, "id": "calculator_1", "position": {"x": 800.5125025564965, "y": 72.40592063242738}, "type": "customNode", "data": {"id": "calculator_1", "label": "Calculator", "version": 1, "name": "calculator", "type": "Calculator", "baseClasses": ["Calculator", "Tool", "StructuredTool", "BaseLangChain"], "category": "Tools", "description": "Perform calculations on response", "inputParams": [], "inputAnchors": [], "inputs": {}, "outputAnchors": [{"id": "calculator_1-output-calculator-Calculator|Tool|StructuredTool|BaseLangChain", "name": "calculator", "label": "Calculator", "type": "Calculator | Tool | StructuredTool | BaseLangChain"}], "outputs": {}, "selected": false}, "positionAbsolute": {"x": 800.5125025564965, "y": 72.40592063242738}, "selected": false, "dragging": false}, {"width": 300, "height": 259, "id": "bufferMemory_1", "position": {"x": 607.6260576768354, "y": 584.7920541862369}, "type": "customNode", "data": {"id": "bufferMemory_1", "label": "Buffer Memory", "version": 2, "name": "bufferMemory", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "baseClasses": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "BaseChatMemory", "BaseMemory"], "category": "Memory", "description": "Retrieve chat messages stored in database", "inputParams": [{"label": "Session Id", "name": "sessionId", "type": "string", "description": "If not specified, a random id will be used. Learn <a target=\"_blank\" href=\"https://docs.flowiseai.com/memory#ui-and-embedded-chat\">more</a>", "default": "", "additionalParams": true, "optional": true, "id": "bufferMemory_1-input-sessionId-string"}, {"label": "Memory Key", "name": "<PERSON><PERSON><PERSON>", "type": "string", "default": "chat_history", "additionalParams": true, "id": "bufferMemory_1-input-memoryKey-string"}], "inputAnchors": [], "inputs": {"sessionId": "", "memoryKey": "chat_history"}, "outputAnchors": [{"id": "bufferMemory_1-output-bufferMemory-BufferMemory|BaseChatMemory|BaseMemory", "name": "bufferMemory", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "BufferMemory | BaseChatMemory | BaseMemory"}], "outputs": {}, "selected": false}, "positionAbsolute": {"x": 607.6260576768354, "y": 584.7920541862369}, "selected": false, "dragging": false}, {"width": 300, "height": 282, "id": "serpAPI_0", "position": {"x": 439.29908455642476, "y": 48.06000078669291}, "type": "customNode", "data": {"id": "serpAPI_0", "label": "Serp API", "version": 1, "name": "serpAPI", "type": "SerpAPI", "baseClasses": ["SerpAPI", "Tool", "StructuredTool"], "category": "Tools", "description": "Wrapper around SerpAPI - a real-time API to access Google search results", "inputParams": [{"label": "Connect Credential", "name": "credential", "type": "credential", "credentialNames": ["serp<PERSON><PERSON>"], "id": "serpAPI_0-input-credential-credential"}], "inputAnchors": [], "inputs": {}, "outputAnchors": [{"id": "serpAPI_0-output-serpAPI-SerpAPI|Tool|StructuredTool", "name": "serpAPI", "label": "SerpAPI", "type": "SerpAPI | Tool | StructuredTool"}], "outputs": {}, "selected": false}, "selected": false, "positionAbsolute": {"x": 439.29908455642476, "y": 48.06000078669291}, "dragging": false}, {"width": 300, "height": 772, "id": "chatOpenAI_0", "position": {"x": 97.01321406237057, "y": 63.67664262280914}, "type": "customNode", "data": {"id": "chatOpenAI_0", "label": "ChatOpenAI", "version": 8.2, "name": "chatOpenAI", "type": "ChatOpenAI", "baseClasses": ["ChatOpenAI", "BaseChatModel", "BaseLanguageModel", "Runnable"], "category": "Chat Models", "description": "Wrapper around OpenAI large language models that use the Chat endpoint", "inputParams": [{"label": "Connect Credential", "name": "credential", "type": "credential", "credentialNames": ["openAIApi"], "id": "chatOpenAI_0-input-credential-credential", "display": true}, {"label": "Model Name", "name": "modelName", "type": "asyncOptions", "loadMethod": "listModels", "default": "gpt-4o-mini", "id": "chatOpenAI_0-input-modelName-asyncOptions", "display": true}, {"label": "Temperature", "name": "temperature", "type": "number", "step": 0.1, "default": 0.9, "optional": true, "id": "chatOpenAI_0-input-temperature-number", "display": true}, {"label": "Streaming", "name": "streaming", "type": "boolean", "default": true, "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-streaming-boolean", "display": true}, {"label": "<PERSON>", "name": "maxTokens", "type": "number", "step": 1, "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-maxTokens-number", "display": true}, {"label": "Top Probability", "name": "topP", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-topP-number", "display": true}, {"label": "Frequency Penalty", "name": "frequencyPenalty", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-frequencyPenalty-number", "display": true}, {"label": "Presence Penalty", "name": "presencePenalty", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-presencePenalty-number", "display": true}, {"label": "Timeout", "name": "timeout", "type": "number", "step": 1, "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-timeout-number", "display": true}, {"label": "Strict Tool Calling", "name": "strictToolCalling", "type": "boolean", "description": "Whether the model supports the `strict` argument when passing in tools. If not specified, the `strict` argument will not be passed to OpenAI.", "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-strictToolCalling-boolean", "display": true}, {"label": "Stop Sequence", "name": "stopSequence", "type": "string", "rows": 4, "optional": true, "description": "List of stop words to use when generating. Use comma to separate multiple stop words.", "additionalParams": true, "id": "chatOpenAI_0-input-stopSequence-string", "display": true}, {"label": "BasePath", "name": "basepath", "type": "string", "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-basepath-string", "display": true}, {"label": "Proxy Url", "name": "proxyUrl", "type": "string", "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-proxyUrl-string", "display": true}, {"label": "BaseOptions", "name": "baseOptions", "type": "json", "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-baseOptions-json", "display": true}, {"label": "Allow Image Uploads", "name": "allowImageUploads", "type": "boolean", "description": "Allow image input. Refer to the <a href=\"https://docs.flowiseai.com/using-flowise/uploads#image\" target=\"_blank\">docs</a> for more details.", "default": false, "optional": true, "id": "chatOpenAI_0-input-allowImageUploads-boolean", "display": true}, {"label": "Image Resolution", "description": "This parameter controls the resolution in which the model views the image.", "name": "imageResolution", "type": "options", "options": [{"label": "Low", "name": "low"}, {"label": "High", "name": "high"}, {"label": "Auto", "name": "auto"}], "default": "low", "optional": false, "show": {"allowImageUploads": true}, "id": "chatOpenAI_0-input-imageResolution-options", "display": true}, {"label": "Reasoning Effort", "description": "Constrains effort on reasoning for reasoning models. Only applicable for o1 and o3 models.", "name": "<PERSON><PERSON><PERSON>ort", "type": "options", "options": [{"label": "Low", "name": "low"}, {"label": "Medium", "name": "medium"}, {"label": "High", "name": "high"}], "default": "medium", "optional": false, "additionalParams": true, "id": "chatOpenAI_0-input-reasoningEffort-options", "display": true}], "inputAnchors": [{"label": "<PERSON><PERSON>", "name": "cache", "type": "BaseCache", "optional": true, "id": "chatOpenAI_0-input-cache-BaseCache", "display": true}], "inputs": {"cache": "", "modelName": "gpt-4o-mini", "temperature": 0.9, "streaming": true, "maxTokens": "", "topP": "", "frequencyPenalty": "", "presencePenalty": "", "timeout": "", "strictToolCalling": "", "stopSequence": "", "basepath": "", "proxyUrl": "", "baseOptions": "", "allowImageUploads": true, "imageResolution": "low", "reasoningEffort": "medium"}, "outputAnchors": [{"id": "chatOpenAI_0-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel|Runnable", "name": "chatOpenAI", "label": "ChatOpenAI", "description": "Wrapper around OpenAI large language models that use the Chat endpoint", "type": "ChatOpenAI | BaseChatModel | BaseLanguageModel | Runnable"}], "outputs": {}, "selected": false}, "selected": false, "positionAbsolute": {"x": 97.01321406237057, "y": 63.67664262280914}, "dragging": false}, {"id": "stickyNote_0", "position": {"x": 1197.3578961103253, "y": 117.43214592301385}, "type": "stickyNote", "data": {"id": "stickyNote_0", "label": "<PERSON><PERSON>", "version": 2, "name": "stickyNote", "type": "StickyNote", "baseClasses": ["StickyNote"], "tags": ["Utilities"], "category": "Utilities", "description": "Add a sticky note", "inputParams": [{"label": "", "name": "note", "type": "string", "rows": 1, "placeholder": "Type something here", "optional": true, "id": "stickyNote_0-input-note-string"}], "inputAnchors": [], "inputs": {"note": "LLM has to be function calling compatible"}, "outputAnchors": [{"id": "stickyNote_0-output-stickyNote-StickyNote", "name": "stickyNote", "label": "StickyNote", "description": "Add a sticky note", "type": "StickyNote"}], "outputs": {}, "selected": false}, "width": 300, "height": 62, "selected": false, "positionAbsolute": {"x": 1197.3578961103253, "y": 117.43214592301385}, "dragging": false}, {"id": "toolAgent_0", "position": {"x": 1200.6756893536506, "y": 208.18578883272318}, "type": "customNode", "data": {"id": "toolAgent_0", "label": "Tool Agent", "version": 2, "name": "toolAgent", "type": "AgentExecutor", "baseClasses": ["AgentExecutor", "BaseChain", "Runnable"], "category": "Agents", "description": "Agent that uses Function Calling to pick the tools and args to call", "inputParams": [{"label": "System Message", "name": "systemMessage", "type": "string", "default": "You are a helpful AI assistant.", "description": "If Chat Prompt Template is provided, this will be ignored", "rows": 4, "optional": true, "additionalParams": true, "id": "toolAgent_0-input-systemMessage-string", "display": true}, {"label": "Max Iterations", "name": "maxIterations", "type": "number", "optional": true, "additionalParams": true, "id": "toolAgent_0-input-maxIterations-number", "display": true}, {"label": "Enable Detailed Streaming", "name": "enableDetailedStreaming", "type": "boolean", "default": false, "description": "Stream detailed intermediate steps during agent execution", "optional": true, "additionalParams": true, "id": "toolAgent_0-input-enableDetailedStreaming-boolean", "display": true}], "inputAnchors": [{"label": "Tools", "name": "tools", "type": "Tool", "list": true, "id": "toolAgent_0-input-tools-Tool", "display": true}, {"label": "Memory", "name": "memory", "type": "BaseChatMemory", "id": "toolAgent_0-input-memory-BaseChatMemory", "display": true}, {"label": "Tool Calling Chat Model", "name": "model", "type": "BaseChatModel", "description": "Only compatible with models that are capable of function calling: ChatOpenAI, ChatMistral, ChatAnthropic, ChatGoogleGenerativeAI, ChatVertexAI, GroqChat", "id": "toolAgent_0-input-model-BaseChatModel", "display": true}, {"label": "Chat Prompt Template", "name": "chatPromptTemplate", "type": "ChatPromptTemplate", "description": "Override existing prompt with Chat Prompt Template. Human Message must includes {input} variable", "optional": true, "id": "toolAgent_0-input-chatPromptTemplate-ChatPromptTemplate", "display": true}, {"label": "Input Moderation", "description": "Detect text that could generate harmful output and prevent it from being sent to the language model", "name": "inputModeration", "type": "Moderation", "optional": true, "list": true, "id": "toolAgent_0-input-inputModeration-Moderation", "display": true}], "inputs": {"tools": ["{{calculator_1.data.instance}}", "{{serpAPI_0.data.instance}}"], "memory": "{{bufferMemory_1.data.instance}}", "model": "{{chatOpenAI_0.data.instance}}", "chatPromptTemplate": "", "systemMessage": "You are a helpful AI assistant.", "inputModeration": "", "maxIterations": "", "enableDetailedStreaming": ""}, "outputAnchors": [{"id": "toolAgent_0-output-toolAgent-AgentExecutor|BaseChain|Runnable", "name": "toolAgent", "label": "AgentExecutor", "description": "Agent that uses Function Calling to pick the tools and args to call", "type": "AgentExecutor | BaseChain | Runnable"}], "outputs": {}, "selected": false}, "width": 300, "height": 492, "selected": false, "positionAbsolute": {"x": 1200.6756893536506, "y": 208.18578883272318}, "dragging": false}], "edges": [{"source": "calculator_1", "sourceHandle": "calculator_1-output-calculator-Calculator|Tool|StructuredTool|BaseLangChain", "target": "toolAgent_0", "targetHandle": "toolAgent_0-input-tools-Tool", "type": "buttonedge", "id": "calculator_1-calculator_1-output-calculator-Calculator|Tool|StructuredTool|BaseLangChain-toolAgent_0-toolAgent_0-input-tools-Tool"}, {"source": "serpAPI_0", "sourceHandle": "serpAPI_0-output-serpAPI-SerpAPI|Tool|StructuredTool", "target": "toolAgent_0", "targetHandle": "toolAgent_0-input-tools-Tool", "type": "buttonedge", "id": "serpAPI_0-serpAPI_0-output-serpAPI-SerpAPI|Tool|StructuredTool-toolAgent_0-toolAgent_0-input-tools-Tool"}, {"source": "chatOpenAI_0", "sourceHandle": "chatOpenAI_0-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel|Runnable", "target": "toolAgent_0", "targetHandle": "toolAgent_0-input-model-BaseChatModel", "type": "buttonedge", "id": "chatOpenAI_0-chatOpenAI_0-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel|Runnable-toolAgent_0-toolAgent_0-input-model-BaseChatModel"}, {"source": "bufferMemory_1", "sourceHandle": "bufferMemory_1-output-bufferMemory-BufferMemory|BaseChatMemory|BaseMemory", "target": "toolAgent_0", "targetHandle": "toolAgent_0-input-memory-BaseChatMemory", "type": "buttonedge", "id": "bufferMemory_1-bufferMemory_1-output-bufferMemory-BufferMemory|BaseChatMemory|BaseMemory-toolAgent_0-toolAgent_0-input-memory-BaseChatMemory"}]}