{"description": "An email reply HITL (human in the loop) agent that can proceed or refine the email with user input", "usecases": ["Human In Loop"], "nodes": [{"id": "startAgentflow_0", "type": "agent<PERSON>low", "position": {"x": -201.62473061824977, "y": 92.61621373702832}, "data": {"id": "startAgentflow_0", "label": "Start", "version": 1.1, "name": "startAgentflow", "type": "Start", "color": "#7EE787", "hideInput": true, "baseClasses": ["Start"], "category": "Agent Flows", "description": "Starting point of the agentflow", "inputParams": [{"label": "Input Type", "name": "startInputType", "type": "options", "options": [{"label": "Chat Input", "name": "chatInput", "description": "Start the conversation with chat input"}, {"label": "Form Input", "name": "formInput", "description": "Start the workflow with form inputs"}], "default": "chatInput", "id": "startAgentflow_0-input-startInputType-options", "display": true}, {"label": "Form Title", "name": "formTitle", "type": "string", "placeholder": "Please Fill Out The Form", "show": {"startInputType": "formInput"}, "id": "startAgentflow_0-input-formTitle-string", "display": true}, {"label": "Form Description", "name": "formDescription", "type": "string", "placeholder": "Complete all fields below to continue", "show": {"startInputType": "formInput"}, "id": "startAgentflow_0-input-formDescription-string", "display": true}, {"label": "Form Input Types", "name": "formInputTypes", "description": "Specify the type of form input", "type": "array", "show": {"startInputType": "formInput"}, "array": [{"label": "Type", "name": "type", "type": "options", "options": [{"label": "String", "name": "string"}, {"label": "Number", "name": "number"}, {"label": "Boolean", "name": "boolean"}, {"label": "Options", "name": "options"}], "default": "string"}, {"label": "Label", "name": "label", "type": "string", "placeholder": "Label for the input"}, {"label": "Variable Name", "name": "name", "type": "string", "placeholder": "Variable name for the input (must be camel case)", "description": "Variable name must be camel case. For example: firstName, lastName, etc."}, {"label": "Add Options", "name": "addOptions", "type": "array", "show": {"formInputTypes[$index].type": "options"}, "array": [{"label": "Option", "name": "option", "type": "string"}]}], "id": "startAgentflow_0-input-formInputTypes-array", "display": true}, {"label": "Ephemeral Memory", "name": "startEphemeralMemory", "type": "boolean", "description": "Start fresh for every execution without past chat history", "optional": true, "display": true}, {"label": "Flow State", "name": "startState", "description": "Runtime state during the execution of the workflow", "type": "array", "optional": true, "array": [{"label": "Key", "name": "key", "type": "string", "placeholder": "Foo"}, {"label": "Value", "name": "value", "type": "string", "placeholder": "Bar"}], "id": "startAgentflow_0-input-startState-array", "display": true}, {"label": "Persist State", "name": "startPersistState", "type": "boolean", "description": "Persist the state in the same session", "optional": true, "id": "startAgentflow_0-input-startPersistState-boolean", "display": true}], "inputAnchors": [], "inputs": {"startInputType": "formInput", "formTitle": "Email Inquiry", "formDescription": "Incoming email inquiry", "formInputTypes": [{"type": "string", "label": "Subject", "name": "subject", "addOptions": ""}, {"type": "string", "label": "Body", "name": "body", "addOptions": ""}, {"type": "string", "label": "From", "name": "from", "addOptions": ""}], "startState": ""}, "outputAnchors": [{"id": "startAgentflow_0-output-startAgentflow", "label": "Start", "name": "startAgentflow"}], "outputs": {}, "selected": false}, "width": 103, "height": 66, "selected": false, "positionAbsolute": {"x": -201.62473061824977, "y": 92.61621373702832}, "dragging": false}, {"id": "agentAgentflow_0", "position": {"x": -61.56009223078007, "y": 76}, "data": {"id": "agentAgentflow_0", "label": "Email Reply Agent", "version": 1, "name": "agentAgentflow", "type": "Agent", "color": "#4DD0E1", "baseClasses": ["Agent"], "category": "Agent Flows", "description": "Dynamically choose and utilize tools during runtime, enabling multi-step reasoning", "inputParams": [{"label": "Model", "name": "agentModel", "type": "asyncOptions", "loadMethod": "listModels", "loadConfig": true, "id": "agentAgentflow_0-input-agentModel-asyncOptions", "display": true}, {"label": "Messages", "name": "agentMessages", "type": "array", "optional": true, "acceptVariable": true, "array": [{"label": "Role", "name": "role", "type": "options", "options": [{"label": "System", "name": "system"}, {"label": "Assistant", "name": "assistant"}, {"label": "Developer", "name": "developer"}, {"label": "User", "name": "user"}]}, {"label": "Content", "name": "content", "type": "string", "acceptVariable": true, "generateInstruction": true, "rows": 4}], "id": "agentAgentflow_0-input-agentMessages-array", "display": true}, {"label": "Tools", "name": "agentTools", "type": "array", "optional": true, "array": [{"label": "Tool", "name": "agentSelectedTool", "type": "asyncOptions", "loadMethod": "listTools", "loadConfig": true}, {"label": "Require Human Input", "name": "agentSelectedToolRequiresHumanInput", "type": "boolean", "optional": true}], "id": "agentAgentflow_0-input-agentTools-array", "display": true}, {"label": "Knowledge (Document Stores)", "name": "agentKnowledgeDocumentStores", "type": "array", "description": "Give your agent context about different document sources. Document stores must be upserted in advance.", "array": [{"label": "Document Store", "name": "documentStore", "type": "asyncOptions", "loadMethod": "listStores"}, {"label": "Describe Knowledge", "name": "docStoreDescription", "type": "string", "generateDocStoreDescription": true, "placeholder": "Describe what the knowledge base is about, this is useful for the AI to know when and how to search for correct information", "rows": 4}, {"label": "Return Source Documents", "name": "returnSourceDocuments", "type": "boolean", "optional": true}], "optional": true, "id": "agentAgentflow_0-input-agentKnowledgeDocumentStores-array", "display": true}, {"label": "Knowledge (Vector Embeddings)", "name": "agentKnowledgeVSEmbeddings", "type": "array", "description": "Give your agent context about different document sources from existing vector stores and embeddings", "array": [{"label": "Vector Store", "name": "vectorStore", "type": "asyncOptions", "loadMethod": "listVectorStores", "loadConfig": true}, {"label": "Embedding Model", "name": "embeddingModel", "type": "asyncOptions", "loadMethod": "listEmbeddings", "loadConfig": true}, {"label": "Knowledge Name", "name": "knowledgeName", "type": "string", "placeholder": "A short name for the knowledge base, this is useful for the AI to know when and how to search for correct information"}, {"label": "Describe Knowledge", "name": "knowledgeDescription", "type": "string", "placeholder": "Describe what the knowledge base is about, this is useful for the AI to know when and how to search for correct information", "rows": 4}, {"label": "Return Source Documents", "name": "returnSourceDocuments", "type": "boolean", "optional": true}], "optional": true, "id": "agentAgentflow_0-input-agentKnowledgeVSEmbeddings-array", "display": true}, {"label": "Enable Memory", "name": "agentEnableMemory", "type": "boolean", "description": "Enable memory for the conversation thread", "default": true, "optional": true, "id": "agentAgentflow_0-input-agentEnableMemory-boolean", "display": true}, {"label": "Memory Type", "name": "agentMemoryType", "type": "options", "options": [{"label": "All Messages", "name": "allMessages", "description": "Retrieve all messages from the conversation"}, {"label": "Window Size", "name": "windowSize", "description": "Uses a fixed window size to surface the last N messages"}, {"label": "Conversation Summary", "name": "conversation<PERSON><PERSON><PERSON><PERSON>", "description": "Summarizes the whole conversation"}, {"label": "Conversation Summary B<PERSON>er", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Summarize conversations once token limit is reached. Default to 2000"}], "optional": true, "default": "allMessages", "show": {"agentEnableMemory": true}, "id": "agentAgentflow_0-input-agentMemoryType-options", "display": true}, {"label": "Window Size", "name": "agentMemoryWindowSize", "type": "number", "default": "20", "description": "Uses a fixed window size to surface the last N messages", "show": {"agentMemoryType": "windowSize"}, "id": "agentAgentflow_0-input-agentMemoryWindowSize-number", "display": false}, {"label": "<PERSON>", "name": "agentMemoryMaxTokenLimit", "type": "number", "default": "2000", "description": "Summarize conversations once token limit is reached. Default to 2000", "show": {"agentMemoryType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "id": "agentAgentflow_0-input-agentMemoryMaxTokenLimit-number", "display": false}, {"label": "Input Message", "name": "agentUserMessage", "type": "string", "description": "Add an input message as user message at the end of the conversation", "rows": 4, "optional": true, "acceptVariable": true, "show": {"agentEnableMemory": true}, "id": "agentAgentflow_0-input-agentUserMessage-string", "display": true}, {"label": "Return Response As", "name": "agentReturnResponseAs", "type": "options", "options": [{"label": "User Message", "name": "userMessage"}, {"label": "Assistant Message", "name": "assistant<PERSON><PERSON><PERSON>"}], "default": "userMessage", "id": "agentAgentflow_0-input-agentReturnResponseAs-options", "display": true}, {"label": "Update Flow State", "name": "agentUpdateState", "description": "Update runtime state during the execution of the workflow", "type": "array", "optional": true, "acceptVariable": true, "array": [{"label": "Key", "name": "key", "type": "asyncOptions", "loadMethod": "listRuntimeStateKeys", "freeSolo": true}, {"label": "Value", "name": "value", "type": "string", "acceptVariable": true, "acceptNodeOutputAsVariable": true}], "id": "agentAgentflow_0-input-agentUpdateState-array", "display": true}], "inputAnchors": [], "inputs": {"agentModel": "chatOpenAI", "agentMessages": [{"role": "system", "content": "<p>You are a customer support agent working in Flowise Inc. Write a professional email reply to user's query. Use the web search tools to get more details about the prospect.</p><p>Always reply as <PERSON>, Customer Support Representative in Flowise. Dont use placeholders.</p>"}], "agentTools": [{"agentSelectedTool": "googleCustomSearch", "agentSelectedToolConfig": {"agentSelectedTool": "googleCustomSearch"}}, {"agentSelectedTool": "currentDateTime", "agentSelectedToolConfig": {"agentSelectedTool": "currentDateTime"}}], "agentKnowledgeDocumentStores": "", "agentEnableMemory": true, "agentMemoryType": "allMessages", "agentUserMessage": "", "agentReturnResponseAs": "userMessage", "agentUpdateState": "", "agentModelConfig": {"cache": "", "modelName": "gpt-4o-mini", "temperature": 0.9, "streaming": true, "maxTokens": "", "topP": "", "frequencyPenalty": "", "presencePenalty": "", "timeout": "", "strictToolCalling": "", "stopSequence": "", "basepath": "", "proxyUrl": "", "baseOptions": "", "allowImageUploads": "", "imageResolution": "low", "reasoningEffort": "medium", "agentModel": "chatOpenAI"}}, "outputAnchors": [{"id": "agentAgentflow_0-output-agentAgentflow", "label": "Agent", "name": "agentAgentflow"}], "outputs": {}, "selected": false}, "type": "agent<PERSON>low", "width": 189, "height": 100, "selected": false, "positionAbsolute": {"x": -61.56009223078007, "y": 76}, "dragging": false}, {"id": "humanInputAgentflow_0", "position": {"x": 156.05666363734434, "y": 86.62266545493773}, "data": {"id": "humanInputAgentflow_0", "label": "Human Input 0", "version": 1, "name": "humanInputAgentflow", "type": "HumanInput", "color": "#6E6EFD", "baseClasses": ["HumanInput"], "category": "Agent Flows", "description": "Request human input, approval or rejection during execution", "inputParams": [{"label": "Description Type", "name": "humanInputDescriptionType", "type": "options", "options": [{"label": "Fixed", "name": "fixed", "description": "Specify a fixed description"}, {"label": "Dynamic", "name": "dynamic", "description": "Use LLM to generate a description"}], "id": "humanInputAgentflow_0-input-humanInputDescriptionType-options", "display": true}, {"label": "Description", "name": "humanInputDescription", "type": "string", "placeholder": "Are you sure you want to proceed?", "acceptVariable": true, "rows": 4, "show": {"humanInputDescriptionType": "fixed"}, "id": "humanInputAgentflow_0-input-humanInputDescription-string", "display": true}, {"label": "Model", "name": "humanInputModel", "type": "asyncOptions", "loadMethod": "listModels", "loadConfig": true, "show": {"humanInputDescriptionType": "dynamic"}, "id": "humanInputAgentflow_0-input-humanInputModel-asyncOptions", "display": false}, {"label": "Prompt", "name": "humanInputModelPrompt", "type": "string", "default": "<p>Summarize the conversation between the user and the assistant, reiterate the last message from the assistant, and ask if user would like to proceed or if they have any feedback. </p>\n<ul>\n<li>Begin by capturing the key points of the conversation, ensuring that you reflect the main ideas and themes discussed.</li>\n<li>Then, clearly reproduce the last message sent by the assistant to maintain continuity. Make sure the whole message is reproduced.</li>\n<li>Finally, ask the user if they would like to proceed, or provide any feedback on the last assistant message</li>\n</ul>\n<h2 id=\"output-format-the-output-should-be-structured-in-three-parts-\">Output Format The output should be structured in three parts in text:</h2>\n<ul>\n<li>A summary of the conversation (1-3 sentences).</li>\n<li>The last assistant message (exactly as it appeared).</li>\n<li>Ask the user if they would like to proceed, or provide any feedback on last assistant message. No other explanation and elaboration is needed.</li>\n</ul>\n", "acceptVariable": true, "generateInstruction": true, "rows": 4, "show": {"humanInputDescriptionType": "dynamic"}, "id": "humanInputAgentflow_0-input-humanInputModelPrompt-string", "display": false}, {"label": "Enable <PERSON>back", "name": "humanInputEnableFeedback", "type": "boolean", "default": true, "id": "humanInputAgentflow_0-input-humanInputEnableFeedback-boolean", "display": true}], "inputAnchors": [], "inputs": {"humanInputDescriptionType": "fixed", "humanInputEnableFeedback": true, "humanInputModelConfig": {"cache": "", "modelName": "gpt-4o-mini", "temperature": 0.9, "streaming": true, "maxTokens": "", "topP": "", "frequencyPenalty": "", "presencePenalty": "", "timeout": "", "strictToolCalling": "", "stopSequence": "", "basepath": "", "proxyUrl": "", "baseOptions": "", "allowImageUploads": "", "imageResolution": "low", "reasoningEffort": "medium", "humanInputModel": "chatOpenAI"}, "humanInputDescription": "<p>Are you sure you want to proceed?</p>"}, "outputAnchors": [{"id": "humanInputAgentflow_0-output-0", "label": "Human Input", "name": "humanInputAgentflow"}, {"id": "humanInputAgentflow_0-output-1", "label": "Human Input", "name": "humanInputAgentflow"}], "outputs": {"humanInputAgentflow": ""}, "selected": false}, "type": "agent<PERSON>low", "width": 167, "height": 80, "selected": false, "positionAbsolute": {"x": 156.05666363734434, "y": 86.62266545493773}, "dragging": false}, {"id": "loopAgentflow_0", "position": {"x": 392.1370040831033, "y": 150.41190827718114}, "data": {"id": "loopAgentflow_0", "label": "Loop back to Agent", "version": 1, "name": "loopAgentflow", "type": "Loop", "color": "#FFA07A", "hideOutput": true, "baseClasses": ["Loop"], "category": "Agent Flows", "description": "Loop back to a previous node", "inputParams": [{"label": "Loop Back To", "name": "loopBackToNode", "type": "asyncOptions", "loadMethod": "listPreviousNodes", "freeSolo": true, "id": "loopAgentflow_0-input-loopBackToNode-asyncOptions", "display": true}, {"label": "Max Loop Count", "name": "max<PERSON>oop<PERSON>ount", "type": "number", "default": 5, "id": "loopAgentflow_0-input-maxLoopCount-number", "display": true}], "inputAnchors": [], "inputs": {"loopBackToNode": "agentAgentflow_0-Email Reply Agent", "maxLoopCount": 5}, "outputAnchors": [], "outputs": {}, "selected": false}, "type": "agent<PERSON>low", "width": 198, "height": 66, "selected": false, "positionAbsolute": {"x": 392.1370040831033, "y": 150.41190827718114}, "dragging": false}, {"id": "toolAgentflow_0", "position": {"x": 607.0106274902857, "y": 44.74028001269521}, "data": {"id": "toolAgentflow_0", "label": "Send Email", "version": 1.1, "name": "toolAgentflow", "type": "Tool", "color": "#d4a373", "baseClasses": ["Tool"], "category": "Agent Flows", "description": "Tools allow LLM to interact with external systems", "inputParams": [{"label": "Tool", "name": "toolAgentflowSelectedTool", "type": "asyncOptions", "loadMethod": "listTools", "loadConfig": true, "id": "toolAgentflow_0-input-toolAgentflowSelectedTool-asyncOptions", "display": true}, {"label": "Tool Input Arguments", "name": "toolInputArgs", "type": "array", "acceptVariable": true, "refresh": true, "array": [{"label": "Input Argument Name", "name": "inputArgName", "type": "asyncOptions", "loadMethod": "listToolInputArgs", "refresh": true}, {"label": "Input Argument Value", "name": "inputArgValue", "type": "string", "acceptVariable": true}], "show": {"toolAgentflowSelectedTool": ".+"}, "id": "toolAgentflow_0-input-toolInputArgs-array", "display": true}, {"label": "Update Flow State", "name": "toolUpdateState", "description": "Update runtime state during the execution of the workflow", "type": "array", "optional": true, "acceptVariable": true, "array": [{"label": "Key", "name": "key", "type": "asyncOptions", "loadMethod": "listRuntimeStateKeys", "freeSolo": true}, {"label": "Value", "name": "value", "type": "string", "acceptVariable": true, "acceptNodeOutputAsVariable": true}], "id": "toolAgentflow_0-input-toolUpdateState-array", "display": true}], "inputAnchors": [], "inputs": {"toolAgentflowSelectedTool": "gmail", "toolInputArgs": [{"inputArgName": "to", "inputArgValue": "<p><span class=\"variable\" data-type=\"mention\" data-id=\"$form.from\" data-label=\"$form.from\">{{ $form.from }}</span> </p>"}, {"inputArgName": "subject", "inputArgValue": "<p>{{ llmAgentflow_0.output.subject }}</p>"}, {"inputArgName": "body", "inputArgValue": "<p>{{ llmAgentflow_0.output.body }}</p>"}], "toolUpdateState": "", "toolAgentflowSelectedToolConfig": {"gmailType": "messages", "messageActions": "[\"sendMessage\"]", "toolAgentflowSelectedTool": "gmail"}, "undefined": ""}, "outputAnchors": [{"id": "toolAgentflow_0-output-toolAgentflow", "label": "Tool", "name": "toolAgentflow"}], "outputs": {}, "selected": false}, "type": "agent<PERSON>low", "width": 143, "height": 68, "selected": false, "positionAbsolute": {"x": 607.0106274902857, "y": 44.74028001269521}, "dragging": false}, {"id": "llmAgentflow_0", "position": {"x": 368.9022119252032, "y": 43.50583396320786}, "data": {"id": "llmAgentflow_0", "label": "Email Subject & Body", "version": 1, "name": "llmAgentflow", "type": "LLM", "color": "#64B5F6", "baseClasses": ["LLM"], "category": "Agent Flows", "description": "Large language models to analyze user-provided inputs and generate responses", "inputParams": [{"label": "Model", "name": "llmModel", "type": "asyncOptions", "loadMethod": "listModels", "loadConfig": true, "id": "llmAgentflow_0-input-llmModel-asyncOptions", "display": true}, {"label": "Messages", "name": "llmMessages", "type": "array", "optional": true, "acceptVariable": true, "array": [{"label": "Role", "name": "role", "type": "options", "options": [{"label": "System", "name": "system"}, {"label": "Assistant", "name": "assistant"}, {"label": "Developer", "name": "developer"}, {"label": "User", "name": "user"}]}, {"label": "Content", "name": "content", "type": "string", "acceptVariable": true, "generateInstruction": true, "rows": 4}], "id": "llmAgentflow_0-input-llmMessages-array", "display": true}, {"label": "Enable Memory", "name": "llmEnableMemory", "type": "boolean", "description": "Enable memory for the conversation thread", "default": true, "optional": true, "id": "llmAgentflow_0-input-llmEnableMemory-boolean", "display": true}, {"label": "Memory Type", "name": "llmMemoryType", "type": "options", "options": [{"label": "All Messages", "name": "allMessages", "description": "Retrieve all messages from the conversation"}, {"label": "Window Size", "name": "windowSize", "description": "Uses a fixed window size to surface the last N messages"}, {"label": "Conversation Summary", "name": "conversation<PERSON><PERSON><PERSON><PERSON>", "description": "Summarizes the whole conversation"}, {"label": "Conversation Summary B<PERSON>er", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Summarize conversations once token limit is reached. Default to 2000"}], "optional": true, "default": "allMessages", "show": {"llmEnableMemory": true}, "id": "llmAgentflow_0-input-llmMemoryType-options", "display": true}, {"label": "Window Size", "name": "llmMemoryWindowSize", "type": "number", "default": "20", "description": "Uses a fixed window size to surface the last N messages", "show": {"llmMemoryType": "windowSize"}, "id": "llmAgentflow_0-input-llmMemoryWindowSize-number", "display": false}, {"label": "<PERSON>", "name": "llmMemoryMaxTokenLimit", "type": "number", "default": "2000", "description": "Summarize conversations once token limit is reached. Default to 2000", "show": {"llmMemoryType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "id": "llmAgentflow_0-input-llmMemoryMaxTokenLimit-number", "display": false}, {"label": "Input Message", "name": "llmUserMessage", "type": "string", "description": "Add an input message as user message at the end of the conversation", "rows": 4, "optional": true, "acceptVariable": true, "show": {"llmEnableMemory": true}, "id": "llmAgentflow_0-input-llmUserMessage-string", "display": true}, {"label": "Return Response As", "name": "llmReturnResponseAs", "type": "options", "options": [{"label": "User Message", "name": "userMessage"}, {"label": "Assistant Message", "name": "assistant<PERSON><PERSON><PERSON>"}], "default": "userMessage", "id": "llmAgentflow_0-input-llmReturnResponseAs-options", "display": true}, {"label": "JSON Structured Output", "name": "llmStructuredOutput", "description": "Instruct the LLM to give output in a JSON structured schema", "type": "array", "optional": true, "acceptVariable": true, "array": [{"label": "Key", "name": "key", "type": "string"}, {"label": "Type", "name": "type", "type": "options", "options": [{"label": "String", "name": "string"}, {"label": "String Array", "name": "stringArray"}, {"label": "Number", "name": "number"}, {"label": "Boolean", "name": "boolean"}, {"label": "Enum", "name": "enum"}, {"label": "JSON Array", "name": "jsonArray"}]}, {"label": "Enum Values", "name": "enum<PERSON><PERSON><PERSON>", "type": "string", "placeholder": "value1, value2, value3", "description": "Enum values. Separated by comma", "optional": true, "show": {"llmStructuredOutput[$index].type": "enum"}}, {"label": "JSON Schema", "name": "jsonSchema", "type": "code", "placeholder": "{\n    \"answer\": {\n        \"type\": \"string\",\n        \"description\": \"Value of the answer\"\n    },\n    \"reason\": {\n        \"type\": \"string\",\n        \"description\": \"Reason for the answer\"\n    },\n    \"optional\": {\n        \"type\": \"boolean\"\n    },\n    \"count\": {\n        \"type\": \"number\"\n    },\n    \"children\": {\n        \"type\": \"array\",\n        \"items\": {\n            \"type\": \"object\",\n            \"properties\": {\n                \"value\": {\n                    \"type\": \"string\",\n                    \"description\": \"Value of the children's answer\"\n                }\n            }\n        }\n    }\n}", "description": "JSON schema for the structured output", "optional": true, "show": {"llmStructuredOutput[$index].type": "jsonArray"}}, {"label": "Description", "name": "description", "type": "string", "placeholder": "Description of the key"}], "id": "llmAgentflow_0-input-llmStructuredOutput-array", "display": true}, {"label": "Update Flow State", "name": "llmUpdateState", "description": "Update runtime state during the execution of the workflow", "type": "array", "optional": true, "acceptVariable": true, "array": [{"label": "Key", "name": "key", "type": "asyncOptions", "loadMethod": "listRuntimeStateKeys", "freeSolo": true}, {"label": "Value", "name": "value", "type": "string", "acceptVariable": true, "acceptNodeOutputAsVariable": true}], "id": "llmAgentflow_0-input-llmUpdateState-array", "display": true}], "inputAnchors": [], "inputs": {"llmModel": "chatOpenAI", "llmMessages": [], "llmEnableMemory": true, "llmMemoryType": "allMessages", "llmUserMessage": "", "llmReturnResponseAs": "userMessage", "llmStructuredOutput": [{"key": "subject", "type": "string", "enumValues": "", "jsonSchema": "", "description": "Subject of the email"}, {"key": "body", "type": "string", "enumValues": "", "jsonSchema": "", "description": "Body of the email"}], "llmUpdateState": "", "llmModelConfig": {"cache": "", "modelName": "gpt-4o-mini", "temperature": 0.9, "streaming": true, "maxTokens": "", "topP": "", "frequencyPenalty": "", "presencePenalty": "", "timeout": "", "strictToolCalling": "", "stopSequence": "", "basepath": "", "proxyUrl": "", "baseOptions": "", "allowImageUploads": "", "imageResolution": "low", "reasoningEffort": "medium", "llmModel": "chatOpenAI"}}, "outputAnchors": [{"id": "llmAgentflow_0-output-llmAgentflow", "label": "LLM", "name": "llmAgentflow"}], "outputs": {}, "selected": false}, "type": "agent<PERSON>low", "width": 209, "height": 72, "selected": false, "positionAbsolute": {"x": 368.9022119252032, "y": 43.50583396320786}, "dragging": false}], "edges": [{"source": "startAgentflow_0", "sourceHandle": "startAgentflow_0-output-startAgentflow", "target": "agentAgentflow_0", "targetHandle": "agentAgentflow_0", "data": {"sourceColor": "#7EE787", "targetColor": "#4DD0E1", "isHumanInput": false}, "type": "agent<PERSON>low", "id": "startAgentflow_0-startAgentflow_0-output-startAgentflow-agentAgentflow_0-agentAgentflow_0"}, {"source": "agentAgentflow_0", "sourceHandle": "agentAgentflow_0-output-agentAgentflow", "target": "humanInputAgentflow_0", "targetHandle": "humanInputAgentflow_0", "data": {"sourceColor": "#4DD0E1", "targetColor": "#6E6EFD", "isHumanInput": false}, "type": "agent<PERSON>low", "id": "agentAgentflow_0-agentAgentflow_0-output-agentAgentflow-humanInputAgentflow_0-humanInputAgentflow_0"}, {"source": "humanInputAgentflow_0", "sourceHandle": "humanInputAgentflow_0-output-1", "target": "loopAgentflow_0", "targetHandle": "loopAgentflow_0", "data": {"sourceColor": "#6E6EFD", "targetColor": "#FFA07A", "edgeLabel": "reject", "isHumanInput": true}, "type": "agent<PERSON>low", "id": "humanInputAgentflow_0-humanInputAgentflow_0-output-1-loopAgentflow_0-loopAgentflow_0"}, {"source": "humanInputAgentflow_0", "sourceHandle": "humanInputAgentflow_0-output-0", "target": "llmAgentflow_0", "targetHandle": "llmAgentflow_0", "data": {"sourceColor": "#6E6EFD", "targetColor": "#64B5F6", "edgeLabel": "proceed", "isHumanInput": true}, "type": "agent<PERSON>low", "id": "humanInputAgentflow_0-humanInputAgentflow_0-output-0-llmAgentflow_0-llmAgentflow_0"}, {"source": "llmAgentflow_0", "sourceHandle": "llmAgentflow_0-output-llmAgentflow", "target": "toolAgentflow_0", "targetHandle": "toolAgentflow_0", "data": {"sourceColor": "#64B5F6", "targetColor": "#d4a373", "isHumanInput": false}, "type": "agent<PERSON>low", "id": "llmAgentflow_0-llmAgentflow_0-output-llmAgentflow-toolAgentflow_0-toolAgentflow_0"}]}