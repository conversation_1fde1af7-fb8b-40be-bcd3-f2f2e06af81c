# Postgres Record Manager

Postgres Record Manager integration for Flowise

## 🌱 Env Variables

| Variable                          | Description                                     | Type    | Default           |
| --------------------------------- | ----------------------------------------------- | ------- | ----------------- |
| POSTGRES_RECORDMANAGER_HOST       | Default `host` for Postgres Record Manager      | String  |                   |
| POSTGRES_RECORDMANAGER_PORT       | Default `port` for Postgres Record Manager      | Number  | 5432              |
| POSTGRES_RECORDMANAGER_USER       | Default `user` for Postgres Record Manager      | String  |                   |
| POSTGRES_RECORDMANAGER_PASSWORD   | Default `password` for Postgres Record Manager  | String  |                   |
| POSTGRES_RECORDMANAGER_DATABASE   | Default `database` for Postgres Record Manager  | String  |                   |
| POSTGRES_RECORDMANAGER_TABLE_NAME | Default `tableName` for Postgres Record Manager | String  | upsertion_records |
| POSTGRES_RECORDMANAGER_SSL        | Default `ssl` for Postgres Vector Store         | Boolean | false             |

## License

Source code in this repository is made available under the [Apache License Version 2.0](https://github.com/FlowiseAI/Flowise/blob/master/LICENSE.md).
