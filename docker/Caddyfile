# Caddyfile for Flowise AI Chatbot
# Automatic HTTPS with Let's Encrypt for bot.subipman.org

bot.subipman.org {
    # Automatic HTTPS with Let's Encrypt
    # <PERSON><PERSON><PERSON> will automatically obtain and renew SSL certificates
    
    # Reverse proxy to Flowise container
    reverse_proxy flowise:3000 {
        # Health check
        health_uri /api/v1/ping
        health_interval 30s
        health_timeout 10s
        
        # Headers for proper proxy handling
        header_up Host {host}
        header_up X-Real-IP {remote_host}
        header_up X-Forwarded-For {remote_host}
        header_up X-Forwarded-Proto {scheme}
        header_up X-Forwarded-Host {host}
    }
    
    # Security headers
    header {
        # HSTS (HTTP Strict Transport Security)
        Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"
        
        # Prevent clickjacking
        X-Frame-Options "SAMEORIGIN"
        
        # XSS Protection
        X-XSS-Protection "1; mode=block"
        
        # Content type sniffing protection
        X-Content-Type-Options "nosniff"
        
        # Referrer policy
        Referrer-Policy "strict-origin-when-cross-origin"
        
        # Content Security Policy (adjust as needed for your chatbot)
        Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' wss: https:; frame-src 'self';"
        
        # Remove server information
        -Server
    }
    
    # Logging
    log {
        output file /var/log/caddy/bot.subipman.org.log {
            roll_size 100mb
            roll_keep 5
            roll_keep_for 720h
        }
        format json
        level INFO
    }
    
    # Error handling
    handle_errors {
        @5xx expression {http.error.status_code} >= 500
        handle @5xx {
            respond "Service temporarily unavailable. Please try again later." 503
        }
        
        @4xx expression {http.error.status_code} >= 400
        handle @4xx {
            respond "Page not found or access denied." 404
        }
    }
    
    # Rate limiting (optional - adjust as needed)
    rate_limit {
        zone static {
            key {remote_host}
            events 100
            window 1m
        }
    }
}

# Redirect www to non-www (optional)
www.bot.subipman.org {
    redir https://bot.subipman.org{uri} permanent
}

# Global options
{
    # Email for Let's Encrypt (replace with your email)
    email <EMAIL>
    
    # Use Let's Encrypt production server
    acme_ca https://acme-v02.api.letsencrypt.org/directory
    
    # Enable automatic HTTPS
    auto_https on
    
    # Admin API (optional - for monitoring)
    admin localhost:2019
}
