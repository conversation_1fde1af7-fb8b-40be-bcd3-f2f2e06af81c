# Production Environment Configuration for bot.subipman.org
# Flowise AI Chatbot for VPN Customer Support

PORT=3000

############################################################################################################
############################################## DATABASE ####################################################
############################################################################################################

DATABASE_PATH=/root/.flowise
# For production, consider using PostgreSQL for better performance and reliability
# DATABASE_TYPE=postgres
# DATABASE_PORT=5432
# DATABASE_HOST=postgres
# DATABASE_NAME=flowise
# DATABASE_USER=flowise
# DATABASE_PASSWORD=your_secure_postgres_password
# DATABASE_SSL=true

############################################################################################################
############################################## SECRET KEYS #################################################
############################################################################################################

SECRETKEY_PATH=/root/.flowise
# Generate a strong encryption key for production
FLOWISE_SECRETKEY_OVERWRITE=CHANGE_THIS_TO_A_SECURE_32_CHAR_KEY

############################################################################################################
############################################## LOGGING #####################################################
############################################################################################################

LOG_PATH=/root/.flowise/logs
LOG_LEVEL=info
# Enable debug only for troubleshooting
# DEBUG=true

############################################################################################################
############################################## STORAGE #####################################################
############################################################################################################

STORAGE_TYPE=local
BLOB_STORAGE_PATH=/root/.flowise/storage

############################################################################################################
############################################## SETTINGS ####################################################
############################################################################################################

# Important: Set NUMBER_OF_PROXIES=1 since we're using Caddy as reverse proxy
NUMBER_OF_PROXIES=1
# Configure CORS for your domain
CORS_ORIGINS=https://bot.subipman.org
IFRAME_ORIGINS=https://bot.subipman.org
FLOWISE_FILE_SIZE_LIMIT=50mb
SHOW_COMMUNITY_NODES=true
# Disable telemetry for privacy
DISABLE_FLOWISE_TELEMETRY=true

############################################################################################################
############################################ AUTH PARAMETERS ###############################################
############################################################################################################

# Set your production URL
APP_URL=https://bot.subipman.org

# IMPORTANT: Generate secure JWT secrets for production
# Use: openssl rand -hex 32
JWT_AUTH_TOKEN_SECRET=CHANGE_THIS_TO_A_SECURE_64_CHAR_HEX_STRING
JWT_REFRESH_TOKEN_SECRET=CHANGE_THIS_TO_ANOTHER_SECURE_64_CHAR_HEX_STRING
JWT_ISSUER=bot.subipman.org
JWT_AUDIENCE=flowise-users
JWT_TOKEN_EXPIRY_IN_MINUTES=360
JWT_REFRESH_TOKEN_EXPIRY_IN_MINUTES=43200
EXPIRE_AUTH_TOKENS_ON_RESTART=false
EXPRESS_SESSION_SECRET=CHANGE_THIS_TO_A_SECURE_SESSION_SECRET

# Password and token security
PASSWORD_SALT_HASH_ROUNDS=12
TOKEN_HASH_SECRET=CHANGE_THIS_TO_A_SECURE_TOKEN_HASH_SECRET

# Email configuration (optional - for user management)
# SMTP_HOST=smtp.your-email-provider.com
# SMTP_PORT=587
# SMTP_USER=<EMAIL>
# SMTP_PASSWORD=your-email-password
# SMTP_SECURE=true
# SENDER_EMAIL=<EMAIL>

############################################################################################################
############################################### PROXY ######################################################
############################################################################################################

# If your server is behind a corporate proxy, uncomment and configure these
# GLOBAL_AGENT_HTTP_PROXY=
# GLOBAL_AGENT_HTTPS_PROXY=
# GLOBAL_AGENT_NO_PROXY=

############################################################################################################
############################################# SECURITY #####################################################
############################################################################################################

# Additional security settings for production
INVITE_TOKEN_EXPIRY_IN_HOURS=24
PASSWORD_RESET_TOKEN_EXPIRY_IN_MINS=15
