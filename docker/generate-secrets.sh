#!/bin/bash

# Generate secure secrets for Flowise production deployment
# This script generates random secure keys and updates the .env.production file

set -e

echo "🔐 Generating secure secrets for Flowise production deployment..."

# Check if .env.production exists
if [ ! -f ".env.production" ]; then
    echo "❌ Error: .env.production file not found!"
    echo "Please make sure you're running this script from the docker directory."
    exit 1
fi

# Create backup of original file
cp .env.production .env.production.backup
echo "📋 Created backup: .env.production.backup"

# Generate secure random strings
generate_hex_key() {
    openssl rand -hex 32
}

generate_base64_key() {
    openssl rand -base64 32 | tr -d "=+/" | cut -c1-32
}

# Generate all required secrets
FLOWISE_SECRETKEY=$(generate_base64_key)
JWT_AUTH_TOKEN_SECRET=$(generate_hex_key)
JWT_REFRESH_TOKEN_SECRET=$(generate_hex_key)
EXPRESS_SESSION_SECRET=$(generate_base64_key)
TOKEN_HASH_SECRET=$(generate_base64_key)

echo "🔑 Generated secure secrets:"
echo "   - Flowise Encryption Key: ✓"
echo "   - JWT Auth Token Secret: ✓"
echo "   - JWT Refresh Token Secret: ✓"
echo "   - Express Session Secret: ✓"
echo "   - Token Hash Secret: ✓"

# Update .env.production file with generated secrets
sed -i "s/FLOWISE_SECRETKEY_OVERWRITE=CHANGE_THIS_TO_A_SECURE_32_CHAR_KEY/FLOWISE_SECRETKEY_OVERWRITE=$FLOWISE_SECRETKEY/" .env.production
sed -i "s/JWT_AUTH_TOKEN_SECRET=CHANGE_THIS_TO_A_SECURE_64_CHAR_HEX_STRING/JWT_AUTH_TOKEN_SECRET=$JWT_AUTH_TOKEN_SECRET/" .env.production
sed -i "s/JWT_REFRESH_TOKEN_SECRET=CHANGE_THIS_TO_ANOTHER_SECURE_64_CHAR_HEX_STRING/JWT_REFRESH_TOKEN_SECRET=$JWT_REFRESH_TOKEN_SECRET/" .env.production
sed -i "s/EXPRESS_SESSION_SECRET=CHANGE_THIS_TO_A_SECURE_SESSION_SECRET/EXPRESS_SESSION_SECRET=$EXPRESS_SESSION_SECRET/" .env.production
sed -i "s/TOKEN_HASH_SECRET=CHANGE_THIS_TO_A_SECURE_TOKEN_HASH_SECRET/TOKEN_HASH_SECRET=$TOKEN_HASH_SECRET/" .env.production

echo "✅ Updated .env.production with secure secrets"

# Prompt for email address for Let's Encrypt
echo ""
echo "📧 Please provide your email address for Let's Encrypt SSL certificates:"
read -p "Email: " EMAIL

if [ ! -z "$EMAIL" ]; then
    # Update Caddyfile with email
    sed -i "s/email <EMAIL>/email $EMAIL/" Caddyfile
    echo "✅ Updated Caddyfile with your email: $EMAIL"
fi

echo ""
echo "🎉 Security configuration completed!"
echo ""
echo "📝 Next steps:"
echo "   1. Review the generated .env.production file"
echo "   2. Update any additional settings as needed"
echo "   3. Run: docker-compose -f docker-compose.production.yml up -d"
echo ""
echo "⚠️  Important: Keep your .env.production file secure and never commit it to version control!"
echo "   The backup file (.env.production.backup) contains the template with placeholder values."
