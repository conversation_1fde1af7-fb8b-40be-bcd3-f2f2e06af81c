#!/bin/bash

# Flowise Production Deployment Script for Ubuntu Server
# This script automates the deployment of Flowise AI chatbot with Caddy HTTPS

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
if [ "$EUID" -eq 0 ]; then
    print_error "Please don't run this script as root. Use a regular user with sudo privileges."
    exit 1
fi

print_status "🚀 Starting Flowise AI Chatbot deployment for bot.subipman.org"

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install Docker first."
    echo "Run: curl -fsSL https://get.docker.com -o get-docker.sh && sh get-docker.sh"
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    print_error "Docker Compose is not installed. Please install Docker Compose first."
    echo "Run: sudo apt-get update && sudo apt-get install docker-compose-plugin"
    exit 1
fi

# Check if user is in docker group
if ! groups $USER | grep &>/dev/null '\bdocker\b'; then
    print_warning "User $USER is not in the docker group. Adding to docker group..."
    sudo usermod -aG docker $USER
    print_warning "Please log out and log back in, then run this script again."
    exit 1
fi

# Create necessary directories
print_status "📁 Creating necessary directories..."
sudo mkdir -p /var/log/caddy
sudo chown $USER:$USER /var/log/caddy

# Check if .env.production exists
if [ ! -f ".env.production" ]; then
    print_error ".env.production file not found!"
    print_status "Please run ./generate-secrets.sh first to create the production environment file."
    exit 1
fi

# Check if secrets have been generated
if grep -q "CHANGE_THIS_TO_A_SECURE" .env.production; then
    print_error "Secrets have not been generated yet!"
    print_status "Please run ./generate-secrets.sh first to generate secure secrets."
    exit 1
fi

# Stop any existing containers
print_status "🛑 Stopping any existing Flowise containers..."
docker-compose -f docker-compose.production.yml down --remove-orphans || true

# Pull latest images
print_status "📥 Pulling latest Docker images..."
docker-compose -f docker-compose.production.yml pull

# Start the services
print_status "🚀 Starting Flowise services..."
docker-compose -f docker-compose.production.yml up -d

# Wait for services to be healthy
print_status "⏳ Waiting for services to be healthy..."
sleep 30

# Check service status
print_status "🔍 Checking service status..."
docker-compose -f docker-compose.production.yml ps

# Test if Flowise is responding
print_status "🧪 Testing Flowise health endpoint..."
if docker exec flowise-app curl -f http://localhost:3000/api/v1/ping > /dev/null 2>&1; then
    print_success "Flowise is responding correctly!"
else
    print_error "Flowise is not responding. Check the logs:"
    echo "docker-compose -f docker-compose.production.yml logs flowise"
    exit 1
fi

# Test if Caddy is responding
print_status "🧪 Testing Caddy health..."
if docker exec flowise-caddy caddy version > /dev/null 2>&1; then
    print_success "Caddy is running correctly!"
else
    print_error "Caddy is not responding. Check the logs:"
    echo "docker-compose -f docker-compose.production.yml logs caddy"
    exit 1
fi

print_success "🎉 Deployment completed successfully!"
echo ""
print_status "📋 Deployment Summary:"
echo "   • Domain: https://bot.subipman.org"
echo "   • SSL: Automatic HTTPS with Let's Encrypt"
echo "   • Services: Flowise AI + Caddy Reverse Proxy"
echo "   • Data: Persistent volumes for data, logs, and storage"
echo ""
print_status "🔧 Useful Commands:"
echo "   • View logs: docker-compose -f docker-compose.production.yml logs -f"
echo "   • Restart: docker-compose -f docker-compose.production.yml restart"
echo "   • Stop: docker-compose -f docker-compose.production.yml down"
echo "   • Update: docker-compose -f docker-compose.production.yml pull && docker-compose -f docker-compose.production.yml up -d"
echo ""
print_status "🌐 Your Flowise AI chatbot should now be available at:"
echo "   https://bot.subipman.org"
echo ""
print_warning "⚠️  Note: It may take a few minutes for Let's Encrypt to issue the SSL certificate."
print_warning "    If you get SSL errors initially, wait 2-3 minutes and try again."
