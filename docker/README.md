# Flowise AI Chatbot - Production Deployment Guide

This guide covers deploying Flowise AI as a production-ready chatbot for VPN customer support with automatic HTTPS using Caddy.

## 🚀 Quick Production Deployment

For production deployment on Ubuntu server with domain `bot.subipman.org`:

### Prerequisites

1. **Ubuntu Server** with Dock<PERSON> and Docker Compose installed
2. **Domain** pointed to your server (bot.subipman.org)
3. **Ports 80 and 443** open in firewall
4. **Non-root user** with sudo privileges

### One-Command Deployment

```bash
# 1. Generate secure secrets
./generate-secrets.sh

# 2. Deploy to production
./deploy.sh
```

Your chatbot will be available at: **https://bot.subipman.org**

## 📋 Detailed Setup Instructions

### Step 1: Server Preparation

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh

# Install Docker Compose
sudo apt-get install docker-compose-plugin

# Add user to docker group
sudo usermod -aG docker $USER

# Log out and log back in for group changes to take effect
```

### Step 2: Clone and Setup

```bash
# Clone the repository
git clone https://github.com/FlowiseAI/Flowise.git
cd Flowise/docker

# Generate production secrets
./generate-secrets.sh

# Deploy
./deploy.sh
```

## 🔧 Configuration Files

### Production Environment (`.env.production`)
- Pre-configured for `bot.subipman.org`
- Secure JWT secrets (auto-generated)
- Production logging settings
- CORS configured for your domain

### Caddy Configuration (`Caddyfile`)
- Automatic HTTPS with Let's Encrypt
- Security headers
- Rate limiting
- Health checks
- Logging

### Docker Compose (`docker-compose.production.yml`)
- Caddy reverse proxy
- Flowise application
- Named volumes for persistence
- Health checks
- Proper networking

## 🌱 Environment Variables

Key production variables in `.env.production`:

-   `APP_URL=https://bot.subipman.org`
-   `CORS_ORIGINS=https://bot.subipman.org`
-   `NUMBER_OF_PROXIES=1` (for Caddy)
-   `LOG_LEVEL=info`
-   `DISABLE_FLOWISE_TELEMETRY=true`

## 🔐 Security Features

- **Automatic HTTPS** with Let's Encrypt
- **Security Headers** (HSTS, XSS Protection, etc.)
- **Rate Limiting** to prevent abuse
- **Secure JWT Secrets** (auto-generated)
- **CORS Protection** for your domain only

## 🛠️ Management Commands

### Service Management
```bash
# View logs
docker-compose -f docker-compose.production.yml logs -f

# Restart services
docker-compose -f docker-compose.production.yml restart

# Stop services
docker-compose -f docker-compose.production.yml down

# Update to latest version
docker-compose -f docker-compose.production.yml pull
docker-compose -f docker-compose.production.yml up -d
```

### Monitoring
```bash
# Check service status
docker-compose -f docker-compose.production.yml ps

# Check health
docker exec flowise-app curl -f http://localhost:3000/api/v1/ping

# View Caddy admin (if enabled)
curl http://localhost:2019/config/
```

### Backup and Restore
```bash
# Backup data
docker run --rm -v flowise_app_data:/data -v $(pwd):/backup alpine tar czf /backup/flowise-backup.tar.gz -C /data .

# Restore data
docker run --rm -v flowise_app_data:/data -v $(pwd):/backup alpine tar xzf /backup/flowise-backup.tar.gz -C /data
```

## 🔍 Troubleshooting

### SSL Certificate Issues
- Wait 2-3 minutes for Let's Encrypt to issue certificate
- Check Caddy logs: `docker-compose -f docker-compose.production.yml logs caddy`
- Ensure ports 80 and 443 are open

### Application Not Starting
- Check Flowise logs: `docker-compose -f docker-compose.production.yml logs flowise`
- Verify environment variables in `.env.production`
- Ensure secrets were generated properly

### Domain Not Resolving
- Verify DNS A record points to your server IP
- Check firewall settings
- Test with: `nslookup bot.subipman.org`

## 📊 Optional Enhancements

### PostgreSQL Database
Uncomment PostgreSQL service in `docker-compose.production.yml` and update `.env.production`:
```bash
DATABASE_TYPE=postgres
DATABASE_HOST=postgres
DATABASE_NAME=flowise
DATABASE_USER=flowise
DATABASE_PASSWORD=your_secure_postgres_password
```

### Redis for Scaling
Uncomment Redis service for session storage and caching.

## 🔄 Development Mode

For local development, use the original setup:

```bash
# Create .env file
cp .env.example .env

# Start development
docker compose up -d

# Access at http://localhost:3000
```

## 📞 Support

For VPN customer support chatbot specific configurations:
1. Configure your AI models in the Flowise UI
2. Set up knowledge bases with VPN documentation
3. Create conversation flows for common support scenarios
4. Test thoroughly before going live

## 🔗 Useful Links

- [Flowise Documentation](https://docs.flowiseai.com/)
- [Caddy Documentation](https://caddyserver.com/docs/)
- [Let's Encrypt](https://letsencrypt.org/)
