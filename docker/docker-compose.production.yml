version: '3.8'

services:
  # Caddy reverse proxy with automatic HTTPS
  caddy:
    image: caddy:2-alpine
    container_name: flowise-caddy
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
      - "2019:2019"  # Admin API (optional)
    volumes:
      - ./Caddyfile:/etc/caddy/Caddyfile:ro
      - caddy_data:/data
      - caddy_config:/config
      - caddy_logs:/var/log/caddy
    environment:
      - CADDY_INGRESS_NETWORKS=flowise-network
    networks:
      - flowise-network
    depends_on:
      - flowise
    healthcheck:
      test: ["CMD", "caddy", "version"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Flowise AI application
  flowise:
    image: flowiseai/flowise:latest
    container_name: flowise-app
    restart: unless-stopped
    env_file:
      - .env.production
    environment:
      # Override specific settings for production
      - PORT=3000
      - NODE_ENV=production
    expose:
      - "3000"  # Only expose to internal network, not to host
    volumes:
      - flowise_data:/root/.flowise
      - flowise_logs:/root/.flowise/logs
      - flowise_storage:/root/.flowise/storage
    networks:
      - flowise-network
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:3000/api/v1/ping']
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    entrypoint: /bin/sh -c "sleep 5; flowise start"

  # Optional: PostgreSQL database for better performance
  # Uncomment if you want to use PostgreSQL instead of SQLite
  # postgres:
  #   image: postgres:15-alpine
  #   container_name: flowise-postgres
  #   restart: unless-stopped
  #   environment:
  #     POSTGRES_DB: flowise
  #     POSTGRES_USER: flowise
  #     POSTGRES_PASSWORD: your_secure_postgres_password
  #   volumes:
  #     - postgres_data:/var/lib/postgresql/data
  #   networks:
  #     - flowise-network
  #   healthcheck:
  #     test: ["CMD-SHELL", "pg_isready -U flowise"]
  #     interval: 30s
  #     timeout: 10s
  #     retries: 5

  # Optional: Redis for session storage and caching
  # Uncomment if you need Redis for scaling
  # redis:
  #   image: redis:7-alpine
  #   container_name: flowise-redis
  #   restart: unless-stopped
  #   command: redis-server --appendonly yes --requirepass your_redis_password
  #   volumes:
  #     - redis_data:/data
  #   networks:
  #     - flowise-network
  #   healthcheck:
  #     test: ["CMD", "redis-cli", "ping"]
  #     interval: 30s
  #     timeout: 10s
  #     retries: 3

networks:
  flowise-network:
    driver: bridge
    name: flowise-network

volumes:
  # Caddy volumes
  caddy_data:
    name: flowise_caddy_data
  caddy_config:
    name: flowise_caddy_config
  caddy_logs:
    name: flowise_caddy_logs
  
  # Flowise volumes
  flowise_data:
    name: flowise_app_data
  flowise_logs:
    name: flowise_app_logs
  flowise_storage:
    name: flowise_app_storage
  
  # Optional database volumes (uncomment if using PostgreSQL/Redis)
  # postgres_data:
  #   name: flowise_postgres_data
  # redis_data:
  #   name: flowise_redis_data
