#!/bin/bash

# Ubuntu Server Setup Script for Flowise AI Chatbot
# This script prepares a fresh Ubuntu server for Flowise deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
if [ "$EUID" -eq 0 ]; then
    print_error "Please don't run this script as root. Use a regular user with sudo privileges."
    exit 1
fi

print_status "🚀 Setting up Ubuntu server for Flowise AI deployment"

# Update system
print_status "📦 Updating system packages..."
sudo apt update && sudo apt upgrade -y

# Install essential packages
print_status "📦 Installing essential packages..."
sudo apt install -y curl wget git unzip software-properties-common apt-transport-https ca-certificates gnupg lsb-release

# Install Docker
print_status "🐳 Installing Docker..."
if ! command -v docker &> /dev/null; then
    curl -fsSL https://get.docker.com -o get-docker.sh
    sh get-docker.sh
    rm get-docker.sh
    print_success "Docker installed successfully"
else
    print_success "Docker is already installed"
fi

# Install Docker Compose
print_status "🐳 Installing Docker Compose..."
if ! command -v docker-compose &> /dev/null; then
    sudo apt-get install -y docker-compose-plugin
    print_success "Docker Compose installed successfully"
else
    print_success "Docker Compose is already installed"
fi

# Add user to docker group
print_status "👤 Adding user to docker group..."
sudo usermod -aG docker $USER

# Configure firewall
print_status "🔥 Configuring UFW firewall..."
if command -v ufw &> /dev/null; then
    sudo ufw --force enable
    sudo ufw allow ssh
    sudo ufw allow 80/tcp
    sudo ufw allow 443/tcp
    sudo ufw reload
    print_success "Firewall configured (SSH, HTTP, HTTPS allowed)"
else
    print_warning "UFW not installed. Please ensure ports 80 and 443 are open."
fi

# Create application directory
print_status "📁 Creating application directory..."
mkdir -p ~/flowise-chatbot
cd ~/flowise-chatbot

# Clone Flowise repository
print_status "📥 Cloning Flowise repository..."
if [ ! -d "Flowise" ]; then
    git clone https://github.com/FlowiseAI/Flowise.git
    print_success "Flowise repository cloned"
else
    print_success "Flowise repository already exists"
fi

# Set up directory structure
cd Flowise/docker

# Make scripts executable
chmod +x generate-secrets.sh deploy.sh

# Create log directory
sudo mkdir -p /var/log/caddy
sudo chown $USER:$USER /var/log/caddy

print_success "🎉 Ubuntu server setup completed!"
echo ""
print_status "📋 Setup Summary:"
echo "   • Docker and Docker Compose installed"
echo "   • User added to docker group"
echo "   • Firewall configured (ports 80, 443 open)"
echo "   • Flowise repository cloned"
echo "   • Scripts made executable"
echo ""
print_warning "⚠️  IMPORTANT: You must log out and log back in for docker group changes to take effect!"
echo ""
print_status "🔧 Next Steps:"
echo "   1. Log out and log back in (or run: newgrp docker)"
echo "   2. cd ~/flowise-chatbot/Flowise/docker"
echo "   3. ./generate-secrets.sh"
echo "   4. ./deploy.sh"
echo ""
print_status "🌐 After deployment, your chatbot will be available at:"
echo "   https://bot.subipman.org"
echo ""
print_status "📚 For more information, see the README.md file in the docker directory."
